{"metadata": {"dbt_schema_version": "https://schemas.getdbt.com/dbt/catalog/v1.json", "dbt_version": "1.10.3", "generated_at": "2025-07-08T02:50:01.299848Z", "invocation_id": "f8a4c3b3-2858-4c17-920f-9801fc10dafd", "invocation_started_at": "2025-07-08T02:49:53.135213Z", "env": {}}, "nodes": {"model.nyc_parking_violations.bronze_parking_violation_codes": {"metadata": {"type": "VIEW", "schema": "main", "name": "bronze_parking_violation_codes", "database": "nyc_parking_violations", "comment": null, "owner": null}, "columns": {"violation_code": {"type": "BIGINT", "index": 1, "name": "violation_code", "comment": null}, "definition": {"type": "VARCHAR", "index": 2, "name": "definition", "comment": null}, "manhattan_96th_st_below": {"type": "BIGINT", "index": 3, "name": "manhattan_96th_st_below", "comment": null}, "all_other_areas": {"type": "BIGINT", "index": 4, "name": "all_other_areas", "comment": null}}, "stats": {"has_stats": {"id": "has_stats", "label": "Has Stats?", "value": false, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "model.nyc_parking_violations.bronze_parking_violation_codes"}, "model.nyc_parking_violations.bronze_parking_violations": {"metadata": {"type": "VIEW", "schema": "main", "name": "bronze_parking_violations", "database": "nyc_parking_violations", "comment": null, "owner": null}, "columns": {"summons_number": {"type": "BIGINT", "index": 1, "name": "summons_number", "comment": null}, "registration_state": {"type": "VARCHAR", "index": 2, "name": "registration_state", "comment": null}, "plate_type": {"type": "VARCHAR", "index": 3, "name": "plate_type", "comment": null}, "issue_date": {"type": "DATE", "index": 4, "name": "issue_date", "comment": null}, "violation_code": {"type": "BIGINT", "index": 5, "name": "violation_code", "comment": null}, "vehicle_body_type": {"type": "VARCHAR", "index": 6, "name": "vehicle_body_type", "comment": null}, "vehicle_make": {"type": "VARCHAR", "index": 7, "name": "vehicle_make", "comment": null}, "issuing_agency": {"type": "VARCHAR", "index": 8, "name": "issuing_agency", "comment": null}, "vehicle_expiration_date": {"type": "BIGINT", "index": 9, "name": "vehicle_expiration_date", "comment": null}, "violation_location": {"type": "BIGINT", "index": 10, "name": "violation_location", "comment": null}, "violation_precinct": {"type": "BIGINT", "index": 11, "name": "violation_precinct", "comment": null}, "issuer_precinct": {"type": "BIGINT", "index": 12, "name": "issuer_precinct", "comment": null}, "issuer_code": {"type": "BIGINT", "index": 13, "name": "issuer_code", "comment": null}, "issuer_command": {"type": "VARCHAR", "index": 14, "name": "issuer_command", "comment": null}, "issuer_squad": {"type": "VARCHAR", "index": 15, "name": "issuer_squad", "comment": null}, "violation_time": {"type": "VARCHAR", "index": 16, "name": "violation_time", "comment": null}, "violation_county": {"type": "VARCHAR", "index": 17, "name": "violation_county", "comment": null}, "violation_legal_code": {"type": "BOOLEAN", "index": 18, "name": "violation_legal_code", "comment": null}, "vehicle_color": {"type": "VARCHAR", "index": 19, "name": "vehicle_color", "comment": null}, "vehicle_year": {"type": "BIGINT", "index": 20, "name": "vehicle_year", "comment": null}}, "stats": {"has_stats": {"id": "has_stats", "label": "Has Stats?", "value": false, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "model.nyc_parking_violations.bronze_parking_violations"}, "model.nyc_parking_violations.gold_ticket_metrics": {"metadata": {"type": "BASE TABLE", "schema": "main", "name": "gold_ticket_metrics", "database": "nyc_parking_violations", "comment": null, "owner": null}, "columns": {"violation_code": {"type": "BIGINT", "index": 1, "name": "violation_code", "comment": null}, "ticket_count": {"type": "BIGINT", "index": 2, "name": "ticket_count", "comment": null}, "total_revenue_usd": {"type": "HUGEINT", "index": 3, "name": "total_revenue_usd", "comment": null}}, "stats": {"has_stats": {"id": "has_stats", "label": "Has Stats?", "value": false, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "model.nyc_parking_violations.gold_ticket_metrics"}, "model.nyc_parking_violations.gold_vehicle_metrics": {"metadata": {"type": "BASE TABLE", "schema": "main", "name": "gold_vehicle_metrics", "database": "nyc_parking_violations", "comment": null, "owner": null}, "columns": {"registration_state": {"type": "VARCHAR", "index": 1, "name": "registration_state", "comment": null}, "ticket_count": {"type": "BIGINT", "index": 2, "name": "ticket_count", "comment": null}}, "stats": {"has_stats": {"id": "has_stats", "label": "Has Stats?", "value": false, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "model.nyc_parking_violations.gold_vehicle_metrics"}, "model.nyc_parking_violations.my_first_model": {"metadata": {"type": "VIEW", "schema": "main", "name": "my_first_model", "database": "nyc_parking_violations", "comment": null, "owner": null}, "columns": {"code": {"type": "BIGINT", "index": 1, "name": "code", "comment": null}, "definition": {"type": "VARCHAR", "index": 2, "name": "definition", "comment": null}, "manhattan_96th_st_below": {"type": "BIGINT", "index": 3, "name": "manhattan_96th_st_below", "comment": null}, "all_other_areas": {"type": "BIGINT", "index": 4, "name": "all_other_areas", "comment": null}}, "stats": {"has_stats": {"id": "has_stats", "label": "Has Stats?", "value": false, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "model.nyc_parking_violations.my_first_model"}, "model.nyc_parking_violations.ref_model": {"metadata": {"type": "VIEW", "schema": "main", "name": "ref_model", "database": "nyc_parking_violations", "comment": null, "owner": null}, "columns": {"code": {"type": "BIGINT", "index": 1, "name": "code", "comment": null}, "definition": {"type": "VARCHAR", "index": 2, "name": "definition", "comment": null}, "manhattan_96th_st_below": {"type": "BIGINT", "index": 3, "name": "manhattan_96th_st_below", "comment": null}, "all_other_areas": {"type": "BIGINT", "index": 4, "name": "all_other_areas", "comment": null}}, "stats": {"has_stats": {"id": "has_stats", "label": "Has Stats?", "value": false, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "model.nyc_parking_violations.ref_model"}, "model.nyc_parking_violations.silver_violation_tickets": {"metadata": {"type": "VIEW", "schema": "main", "name": "silver_violation_tickets", "database": "nyc_parking_violations", "comment": null, "owner": null}, "columns": {"summons_number": {"type": "BIGINT", "index": 1, "name": "summons_number", "comment": null}, "issue_date": {"type": "DATE", "index": 2, "name": "issue_date", "comment": null}, "violation_code": {"type": "BIGINT", "index": 3, "name": "violation_code", "comment": null}, "is_manhattan_96th_st_below": {"type": "BOOLEAN", "index": 4, "name": "is_manhattan_96th_st_below", "comment": null}, "issuing_agency": {"type": "VARCHAR", "index": 5, "name": "issuing_agency", "comment": null}, "violation_location": {"type": "BIGINT", "index": 6, "name": "violation_location", "comment": null}, "violation_precinct": {"type": "BIGINT", "index": 7, "name": "violation_precinct", "comment": null}, "issuer_precinct": {"type": "BIGINT", "index": 8, "name": "issuer_precinct", "comment": null}, "issuer_code": {"type": "BIGINT", "index": 9, "name": "issuer_code", "comment": null}, "issuer_command": {"type": "VARCHAR", "index": 10, "name": "issuer_command", "comment": null}, "issuer_squad": {"type": "VARCHAR", "index": 11, "name": "issuer_squad", "comment": null}, "violation_time": {"type": "VARCHAR", "index": 12, "name": "violation_time", "comment": null}, "violation_county": {"type": "VARCHAR", "index": 13, "name": "violation_county", "comment": null}, "violation_legal_code": {"type": "BOOLEAN", "index": 14, "name": "violation_legal_code", "comment": null}, "fee_usd": {"type": "BIGINT", "index": 15, "name": "fee_usd", "comment": null}}, "stats": {"has_stats": {"id": "has_stats", "label": "Has Stats?", "value": false, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "model.nyc_parking_violations.silver_violation_tickets"}, "model.nyc_parking_violations.silver_violation_vehicles": {"metadata": {"type": "VIEW", "schema": "main", "name": "silver_violation_vehicles", "database": "nyc_parking_violations", "comment": null, "owner": null}, "columns": {"summons_number": {"type": "BIGINT", "index": 1, "name": "summons_number", "comment": null}, "registration_state": {"type": "VARCHAR", "index": 2, "name": "registration_state", "comment": null}, "plate_type": {"type": "VARCHAR", "index": 3, "name": "plate_type", "comment": null}, "vehicle_body_type": {"type": "VARCHAR", "index": 4, "name": "vehicle_body_type", "comment": null}, "vehicle_make": {"type": "VARCHAR", "index": 5, "name": "vehicle_make", "comment": null}, "vehicle_expiration_date": {"type": "BIGINT", "index": 6, "name": "vehicle_expiration_date", "comment": null}, "vehicle_color": {"type": "VARCHAR", "index": 7, "name": "vehicle_color", "comment": null}, "vehicle_year": {"type": "BIGINT", "index": 8, "name": "vehicle_year", "comment": null}}, "stats": {"has_stats": {"id": "has_stats", "label": "Has Stats?", "value": false, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "model.nyc_parking_violations.silver_violation_vehicles"}, "test.nyc_parking_violations.generic_not_null_bronze_parking_violations_summons_number.e84297fd3d": {"metadata": {"type": "BASE TABLE", "schema": "main_dbt_test__audit", "name": "generic_not_null_bronze_parking_violations_summons_number", "database": "nyc_parking_violations", "comment": null, "owner": null}, "columns": {"summons_number": {"type": "BIGINT", "index": 1, "name": "summons_number", "comment": null}, "registration_state": {"type": "VARCHAR", "index": 2, "name": "registration_state", "comment": null}, "plate_type": {"type": "VARCHAR", "index": 3, "name": "plate_type", "comment": null}, "issue_date": {"type": "DATE", "index": 4, "name": "issue_date", "comment": null}, "violation_code": {"type": "BIGINT", "index": 5, "name": "violation_code", "comment": null}, "vehicle_body_type": {"type": "VARCHAR", "index": 6, "name": "vehicle_body_type", "comment": null}, "vehicle_make": {"type": "VARCHAR", "index": 7, "name": "vehicle_make", "comment": null}, "issuing_agency": {"type": "VARCHAR", "index": 8, "name": "issuing_agency", "comment": null}, "vehicle_expiration_date": {"type": "BIGINT", "index": 9, "name": "vehicle_expiration_date", "comment": null}, "violation_location": {"type": "BIGINT", "index": 10, "name": "violation_location", "comment": null}, "violation_precinct": {"type": "BIGINT", "index": 11, "name": "violation_precinct", "comment": null}, "issuer_precinct": {"type": "BIGINT", "index": 12, "name": "issuer_precinct", "comment": null}, "issuer_code": {"type": "BIGINT", "index": 13, "name": "issuer_code", "comment": null}, "issuer_command": {"type": "VARCHAR", "index": 14, "name": "issuer_command", "comment": null}, "issuer_squad": {"type": "VARCHAR", "index": 15, "name": "issuer_squad", "comment": null}, "violation_time": {"type": "VARCHAR", "index": 16, "name": "violation_time", "comment": null}, "violation_county": {"type": "VARCHAR", "index": 17, "name": "violation_county", "comment": null}, "violation_legal_code": {"type": "BOOLEAN", "index": 18, "name": "violation_legal_code", "comment": null}, "vehicle_color": {"type": "VARCHAR", "index": 19, "name": "vehicle_color", "comment": null}, "vehicle_year": {"type": "BIGINT", "index": 20, "name": "vehicle_year", "comment": null}}, "stats": {"has_stats": {"id": "has_stats", "label": "Has Stats?", "value": false, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "test.nyc_parking_violations.generic_not_null_bronze_parking_violations_summons_number.e84297fd3d"}, "test.nyc_parking_violations.not_null_bronze_parking_violations_summons_number.29cef758ac": {"metadata": {"type": "BASE TABLE", "schema": "main_dbt_test__audit", "name": "not_null_bronze_parking_violations_summons_number", "database": "nyc_parking_violations", "comment": null, "owner": null}, "columns": {"summons_number": {"type": "BIGINT", "index": 1, "name": "summons_number", "comment": null}, "registration_state": {"type": "VARCHAR", "index": 2, "name": "registration_state", "comment": null}, "plate_type": {"type": "VARCHAR", "index": 3, "name": "plate_type", "comment": null}, "issue_date": {"type": "DATE", "index": 4, "name": "issue_date", "comment": null}, "violation_code": {"type": "BIGINT", "index": 5, "name": "violation_code", "comment": null}, "vehicle_body_type": {"type": "VARCHAR", "index": 6, "name": "vehicle_body_type", "comment": null}, "vehicle_make": {"type": "VARCHAR", "index": 7, "name": "vehicle_make", "comment": null}, "issuing_agency": {"type": "VARCHAR", "index": 8, "name": "issuing_agency", "comment": null}, "vehicle_expiration_date": {"type": "BIGINT", "index": 9, "name": "vehicle_expiration_date", "comment": null}, "violation_location": {"type": "BIGINT", "index": 10, "name": "violation_location", "comment": null}, "violation_precinct": {"type": "BIGINT", "index": 11, "name": "violation_precinct", "comment": null}, "issuer_precinct": {"type": "BIGINT", "index": 12, "name": "issuer_precinct", "comment": null}, "issuer_code": {"type": "BIGINT", "index": 13, "name": "issuer_code", "comment": null}, "issuer_command": {"type": "VARCHAR", "index": 14, "name": "issuer_command", "comment": null}, "issuer_squad": {"type": "VARCHAR", "index": 15, "name": "issuer_squad", "comment": null}, "violation_time": {"type": "VARCHAR", "index": 16, "name": "violation_time", "comment": null}, "violation_county": {"type": "VARCHAR", "index": 17, "name": "violation_county", "comment": null}, "violation_legal_code": {"type": "BOOLEAN", "index": 18, "name": "violation_legal_code", "comment": null}, "vehicle_color": {"type": "VARCHAR", "index": 19, "name": "vehicle_color", "comment": null}, "vehicle_year": {"type": "BIGINT", "index": 20, "name": "vehicle_year", "comment": null}}, "stats": {"has_stats": {"id": "has_stats", "label": "Has Stats?", "value": false, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "test.nyc_parking_violations.not_null_bronze_parking_violations_summons_number.29cef758ac"}, "test.nyc_parking_violations.unique_bronze_parking_violations_summons_number.305522a239": {"metadata": {"type": "BASE TABLE", "schema": "main_dbt_test__audit", "name": "unique_bronze_parking_violations_summons_number", "database": "nyc_parking_violations", "comment": null, "owner": null}, "columns": {"unique_field": {"type": "BIGINT", "index": 1, "name": "unique_field", "comment": null}, "n_records": {"type": "BIGINT", "index": 2, "name": "n_records", "comment": null}}, "stats": {"has_stats": {"id": "has_stats", "label": "Has Stats?", "value": false, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "test.nyc_parking_violations.unique_bronze_parking_violations_summons_number.305522a239"}, "test.nyc_parking_violations.violation_codes_revenue": {"metadata": {"type": "BASE TABLE", "schema": "main_dbt_test__audit", "name": "violation_codes_revenue", "database": "nyc_parking_violations", "comment": null, "owner": null}, "columns": {"violation_code": {"type": "BIGINT", "index": 1, "name": "violation_code", "comment": null}, "total_revenue_usd": {"type": "HUGEINT", "index": 2, "name": "total_revenue_usd", "comment": null}}, "stats": {"has_stats": {"id": "has_stats", "label": "Has Stats?", "value": false, "include": false, "description": "Indicates whether there are statistics for this table"}}, "unique_id": "test.nyc_parking_violations.violation_codes_revenue"}}, "sources": {}, "errors": null}