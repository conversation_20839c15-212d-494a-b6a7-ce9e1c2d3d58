{"editor.bracketPairColorization.enabled": true, "editor.cursorBlinking": "solid", "editor.fontFamily": "ui-monospace, Menlo, Monaco, 'Cascadia Mono', 'Segoe UI Mono', 'Roboto Mono', 'Oxygen Mono', 'Ubuntu Monospace', 'Source Code Pro', 'Fira Mono', 'Droid Sans Mono', 'Courier New', monospace", "editor.fontLigatures": false, "editor.fontSize": 22, "editor.formatOnPaste": true, "editor.formatOnSave": true, "editor.lineNumbers": "on", "editor.matchBrackets": "always", "editor.minimap.enabled": false, "editor.smoothScrolling": true, "editor.tabSize": 2, "editor.useTabStops": true, "emmet.triggerExpansionOnTab": true, "explorer.openEditors.visible": 0, "files.autoSave": "after<PERSON>elay", "screencastMode.onlyKeyboardShortcuts": true, "terminal.integrated.fontSize": 18, "workbench.colorTheme": "Light", "workbench.fontAliasing": "antialiased", "workbench.statusBar.visible": true}