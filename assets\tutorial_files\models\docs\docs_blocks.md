{% docs violation_code %}
Code representing the specific parking violation.
{% enddocs %}

{% docs definition %}
Description of the violation for a respective code.
{% enddocs %}

{% docs manhattan_96th_st_below %}
The fee in $USD for a violation on or below Manhattan 96th Street.
{% enddocs %}

{% docs all_other_areas %}
The fee in $USD for a violation not on or below Manhattan 96th Street.
{% enddocs %}

{% docs summons_number %}
Unique identifier for each summons issued for a parking violation.
{% enddocs %}

{% docs registration_state %}
The state where the vehicle is registered.
{% enddocs %}

{% docs plate_type %}
The type of license plate.
{% enddocs %}

{% docs issue_date %}
The date when the summons was issued.
{% enddocs %}

{% docs vehicle_body_type %}
The body type of the vehicle involved in the violation.
{% enddocs %}

{% docs vehicle_make %}
The make or brand of the vehicle.
{% enddocs %}

{% docs issuing_agency %}
The agency that issued the summons.
{% enddocs %}

{% docs vehicle_expiration_date %}
The date when the vehicle's registration expires.
{% enddocs %}

{% docs violation_location %}
General location where the violation occurred.
{% enddocs %}

{% docs violation_precinct %}
Precinct where the violation was identified.
{% enddocs %}

{% docs issuer_precinct %}
Precinct of the officer or official who issued the summons.
{% enddocs %}

{% docs issuer_code %}
Unique code identifying the issuer.
{% enddocs %}

{% docs issuer_command %}
Command or unit of the issuer.
{% enddocs %}

{% docs issuer_squad %}
Squad detail for the issuer.
{% enddocs %}

{% docs violation_time %}
Time when the violation occurred.
{% enddocs %}

{% docs violation_county %}
County where the violation took place.
{% enddocs %}

{% docs violation_legal_code %}
Legal code associated with the violation.
{% enddocs %}

{% docs vehicle_color %}
Color of the vehicle involved in the violation.
{% enddocs %}

{% docs vehicle_year %}
Manufacturing year of the vehicle.
{% enddocs %}

{% docs fee_usd %}
The fee charged for a parking violation, specified in USD. This fee varies depending on the location of the violation.
{% enddocs %}

{% docs is_manhattan_96th_st_below %}
A boolean value indicating whether the violation occurred in Manhattan on or below 96th Street.
{% enddocs %}

{% docs ticket_count %}
The total number of tickets issued for a specific violation code.
{% enddocs %}

{% docs total_revenue_usd %}
The total revenue accumulated from tickets, based on the violation code. This sum is represented in USD.
{% enddocs %}
