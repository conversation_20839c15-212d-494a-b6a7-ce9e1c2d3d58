import duckdb
import pandas as pd

nyc_db_path = r"E:\Study Space\Data Engineering\Projects\data-engineering-with-data-build-tool-dbt-4458303\data\nyc_parking_violations.db"

import duckdb

def drop_all_tables(db_path):
    """
    Connects to a DuckDB database, retrieves a list of all tables,
    and then drops each one.
    """
    with duckdb.connect(db_path) as con:
        # Get a list of all tables
        # DuckDB's information_schema.tables or PRAGMA show_tables() can be used
        tables_df = con.sql("SELECT table_name FROM information_schema.tables WHERE table_schema = 'main';").df()

        if tables_df.empty:
            print(f"No tables found in database: {db_path}")
            return

        table_names = tables_df['table_name'].tolist()

        print(f"Found tables to drop in {db_path}: {table_names}")

        for table_name in table_names:
            try:
                # Use DROP TABLE IF EXISTS to avoid errors if a table doesn't exist
                # (though in this loop, they should)
                con.sql(f"DROP TABLE IF EXISTS {table_name};")
                print(f"Dropped table: {table_name}")
            except Exception as e:
                print(f"Error dropping table {table_name}: {e}")

# Call the function to drop all tables
drop_all_tables(nyc_db_path)

# Verify that tables are dropped
with duckdb.connect(nyc_db_path) as con:
    print("\nTables after dropping:")
    display(con.sql("SHOW TABLES;"))

sql_query = '''
SELECT * FROM gold_vehicle_metrics LIMIT 10
'''
with duckdb.connect(nyc_db_path) as con:
    display(con.sql(sql_query).df())

insert_parking_violation_codes_sql = r"""
CREATE OR REPLACE TABLE parking_violation_codes AS 
SELECT * FROM read_csv_auto(
'E:\Study Space\Data Engineering\Projects\data-engineering-with-data-build-tool-dbt-4458303\data\dof_parking_violation_codes.csv',
normalize_names=True
)
"""

insert_parking_violations_issued_fy2023_sql = r"""
CREATE OR REPLACE TABLE parking_violation_2023 AS 
SELECT * FROM read_csv_auto(
'E:\Study Space\Data Engineering\Projects\data-engineering-with-data-build-tool-dbt-4458303\data\parking_violations_issued_fiscal_year_2023_sample.csv',
normalize_names=True
)
"""

with duckdb.connect(nyc_db_path) as con:
    display(con.sql(insert_parking_violation_codes_sql))
    display(con.sql(insert_parking_violations_issued_fy2023_sql))