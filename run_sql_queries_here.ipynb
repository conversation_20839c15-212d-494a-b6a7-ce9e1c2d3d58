{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import duckdb\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["nyc_db_path = r\"E:\\Study Space\\Data Engineering\\Projects\\data-engineering-with-data-build-tool-dbt-4458303\\data\\nyc_parking_violations.db\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Found tables to drop in E:\\Study Space\\Data Engineering\\Projects\\data-engineering-with-data-build-tool-dbt-4458303\\data\\nyc_parking_violations.db: ['bronze_parking_violations', 'bronze_parking_violation_codes', 'first_model', 'ref_model', 'silver_parking_violations', 'silver_parking_violation_codes', 'silver_violation_tickets', 'silver_violation_vehicles']\n", "Dropped table: bronze_parking_violations\n", "Dropped table: bronze_parking_violation_codes\n", "Dropped table: first_model\n", "Dropped table: ref_model\n", "Dropped table: silver_parking_violations\n", "Dropped table: silver_parking_violation_codes\n", "Dropped table: silver_violation_tickets\n", "Dropped table: silver_violation_vehicles\n", "\n", "Tables after dropping:\n"]}, {"data": {"text/plain": ["┌─────────┐\n", "│  name   │\n", "│ varchar │\n", "├─────────┤\n", "│ 0 rows  │\n", "└─────────┘"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import duckdb\n", "\n", "def drop_all_tables(db_path):\n", "    \"\"\"\n", "    Connects to a DuckDB database, retrieves a list of all tables,\n", "    and then drops each one.\n", "    \"\"\"\n", "    with duckdb.connect(db_path) as con:\n", "        # Get a list of all tables\n", "        # DuckDB's information_schema.tables or PRAGMA show_tables() can be used\n", "        tables_df = con.sql(\"SELECT table_name FROM information_schema.tables WHERE table_schema = 'main';\").df()\n", "\n", "        if tables_df.empty:\n", "            print(f\"No tables found in database: {db_path}\")\n", "            return\n", "\n", "        table_names = tables_df['table_name'].tolist()\n", "\n", "        print(f\"Found tables to drop in {db_path}: {table_names}\")\n", "\n", "        for table_name in table_names:\n", "            try:\n", "                # Use DROP TABLE IF EXISTS to avoid errors if a table doesn't exist\n", "                # (though in this loop, they should)\n", "                con.sql(f\"DROP TABLE IF EXISTS {table_name};\")\n", "                print(f\"Dropped table: {table_name}\")\n", "            except Exception as e:\n", "                print(f\"Error dropping table {table_name}: {e}\")\n", "\n", "# Call the function to drop all tables\n", "drop_all_tables(nyc_db_path)\n", "\n", "# Verify that tables are dropped\n", "with duckdb.connect(nyc_db_path) as con:\n", "    print(\"\\nTables after dropping:\")\n", "    display(con.sql(\"SHOW TABLES;\"))"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>registration_state</th>\n", "      <th>ticket_count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>NJ</td>\n", "      <td>9258</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>PA</td>\n", "      <td>3514</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>FL</td>\n", "      <td>2414</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>CT</td>\n", "      <td>1787</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>GA</td>\n", "      <td>840</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>VA</td>\n", "      <td>797</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>MA</td>\n", "      <td>788</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>IN</td>\n", "      <td>765</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>NC</td>\n", "      <td>614</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>TX</td>\n", "      <td>594</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  registration_state  ticket_count\n", "0                 NJ          9258\n", "1                 PA          3514\n", "2                 FL          2414\n", "3                 CT          1787\n", "4                 GA           840\n", "5                 VA           797\n", "6                 MA           788\n", "7                 IN           765\n", "8                 NC           614\n", "9                 TX           594"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sql_query = '''\n", "SELECT * FROM gold_vehicle_metrics LIMIT 10\n", "'''\n", "with duckdb.connect(nyc_db_path) as con:\n", "    display(con.sql(sql_query).df())"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["None"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["None"]}, "metadata": {}, "output_type": "display_data"}], "source": ["insert_parking_violation_codes_sql = r\"\"\"\n", "CREATE OR REPLACE TABLE parking_violation_codes AS \n", "SELECT * FROM read_csv_auto(\n", "'E:\\Study Space\\Data Engineering\\Projects\\data-engineering-with-data-build-tool-dbt-4458303\\data\\dof_parking_violation_codes.csv',\n", "normalize_names=True\n", ")\n", "\"\"\"\n", "\n", "insert_parking_violations_issued_fy2023_sql = r\"\"\"\n", "CREATE OR REPLACE TABLE parking_violation_2023 AS \n", "SELECT * FROM read_csv_auto(\n", "'E:\\Study Space\\Data Engineering\\Projects\\data-engineering-with-data-build-tool-dbt-4458303\\data\\parking_violations_issued_fiscal_year_2023_sample.csv',\n", "normalize_names=True\n", ")\n", "\"\"\"\n", "\n", "with duckdb.connect(nyc_db_path) as con:\n", "    display(con.sql(insert_parking_violation_codes_sql))\n", "    display(con.sql(insert_parking_violations_issued_fy2023_sql))"]}], "metadata": {"kernelspec": {"display_name": ".dbt_venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}