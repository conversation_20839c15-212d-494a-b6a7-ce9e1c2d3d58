{"metadata": {"dbt_schema_version": "https://schemas.getdbt.com/dbt/run-results/v6.json", "dbt_version": "1.10.3", "generated_at": "2025-07-08T02:52:30.782694Z", "invocation_id": "e605303b-4492-426b-b396-5d190887e742", "invocation_started_at": "2025-07-08T02:52:27.601569Z", "env": {}}, "results": [{"status": "pass", "timing": [{"name": "compile", "started_at": "2025-07-08T02:52:29.399697Z", "completed_at": "2025-07-08T02:52:29.460781Z"}, {"name": "execute", "started_at": "2025-07-08T02:52:29.461377Z", "completed_at": "2025-07-08T02:52:30.342008Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.9469611644744873, "adapter_response": {"_message": "OK"}, "message": null, "failures": 0, "unique_id": "test.nyc_parking_violations.generic_not_null_bronze_parking_violations_summons_number.e84297fd3d", "compiled": true, "compiled_code": "\n\n    select *\n    from \"nyc_parking_violations\".\"main\".\"bronze_parking_violations\"\n    where summons_number is null\n\n", "relation_name": "\"nyc_parking_violations\".\"main_dbt_test__audit\".\"generic_not_null_bronze_parking_violations_summons_number\"", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-07-08T02:52:30.350762Z", "completed_at": "2025-07-08T02:52:30.359841Z"}, {"name": "execute", "started_at": "2025-07-08T02:52:30.360603Z", "completed_at": "2025-07-08T02:52:30.447588Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.10024189949035645, "adapter_response": {"_message": "OK"}, "message": null, "failures": 0, "unique_id": "test.nyc_parking_violations.not_null_bronze_parking_violations_summons_number.29cef758ac", "compiled": true, "compiled_code": "\n    \n    \n\n\n\nselect *\nfrom \"nyc_parking_violations\".\"main\".\"bronze_parking_violations\"\nwhere summons_number is null\n\n\n", "relation_name": "\"nyc_parking_violations\".\"main_dbt_test__audit\".\"not_null_bronze_parking_violations_summons_number\"", "batch_results": null}, {"status": "pass", "timing": [{"name": "compile", "started_at": "2025-07-08T02:52:30.453092Z", "completed_at": "2025-07-08T02:52:30.458846Z"}, {"name": "execute", "started_at": "2025-07-08T02:52:30.459264Z", "completed_at": "2025-07-08T02:52:30.604185Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.15494275093078613, "adapter_response": {"_message": "OK"}, "message": null, "failures": 0, "unique_id": "test.nyc_parking_violations.unique_bronze_parking_violations_summons_number.305522a239", "compiled": true, "compiled_code": "\n    \n    \n\nselect\n    summons_number as unique_field,\n    count(*) as n_records\n\nfrom \"nyc_parking_violations\".\"main\".\"bronze_parking_violations\"\nwhere summons_number is not null\ngroup by summons_number\nhaving count(*) > 1\n\n\n", "relation_name": "\"nyc_parking_violations\".\"main_dbt_test__audit\".\"unique_bronze_parking_violations_summons_number\"", "batch_results": null}, {"status": "warn", "timing": [{"name": "compile", "started_at": "2025-07-08T02:52:30.611164Z", "completed_at": "2025-07-08T02:52:30.618500Z"}, {"name": "execute", "started_at": "2025-07-08T02:52:30.618919Z", "completed_at": "2025-07-08T02:52:30.770264Z"}], "thread_id": "Thread-1 (worker)", "execution_time": 0.16309523582458496, "adapter_response": {"_message": "OK"}, "message": "Got 1 result, configured to warn if != 0", "failures": 1, "unique_id": "test.nyc_parking_violations.violation_codes_revenue", "compiled": true, "compiled_code": "\n\nwith __dbt__cte__silver_parking_violation_codes as (\nWITH manhattan_violation_codes AS (\n    SELECT\n        violation_code,\n        definition,\n        TRUE AS is_manhattan_96th_st_below,\n        manhattan_96th_st_below AS fee_usd,\n    FROM\n        \"nyc_parking_violations\".\"main\".\"bronze_parking_violation_codes\"\n),\n\nall_other_violation_codes AS (\n    SELECT\n        violation_code,\n        definition,\n        FALSE AS is_manhattan_96th_st_below,\n        all_other_areas AS fee_usd,\n    FROM\n        \"nyc_parking_violations\".\"main\".\"bronze_parking_violation_codes\"\n)\n\nSELECT * FROM manhattan_violation_codes\nUNION ALL\nSELECT * FROM all_other_violation_codes\nORDER BY violation_code ASC\n) SELECT\n    violation_code,\n    SUM(fee_usd) AS total_revenue_usd\nFROM\n    __dbt__cte__silver_parking_violation_codes\nGROUP BY\n    violation_code\nHAVING\n    NOT(total_revenue_usd >= 1)", "relation_name": "\"nyc_parking_violations\".\"main_dbt_test__audit\".\"violation_codes_revenue\"", "batch_results": null}], "elapsed_time": 1.5606420040130615, "args": {"invocation_command": "dbt test", "log_format_file": "debug", "vars": {}, "which": "test", "skip_nodes_if_on_run_start_fails": false, "require_all_warnings_handled_by_warn_error": false, "select": [], "send_anonymous_usage_stats": true, "version_check": true, "state_modified_compare_vars": false, "exclude_resource_types": [], "printer_width": 80, "upload_to_artifacts_ingest_api": false, "show_resource_report": false, "log_level_file": "debug", "validate_macro_args": false, "require_yaml_configuration_for_mf_time_spines": false, "partial_parse_file_diff": true, "static_parser": true, "defer": false, "strict_mode": false, "show_all_deprecations": false, "cache_selected_only": false, "require_batched_execution_for_custom_microbatch_strategy": false, "profiles_dir": "E:\\Study Space\\Data Engineering\\Projects\\data-engineering-with-data-build-tool-dbt-4458303\\nyc_parking_violations", "partial_parse": true, "log_path": "E:\\Study Space\\Data Engineering\\Projects\\data-engineering-with-data-build-tool-dbt-4458303\\nyc_parking_violations\\logs", "project_dir": "E:\\Study Space\\Data Engineering\\Projects\\data-engineering-with-data-build-tool-dbt-4458303\\nyc_parking_violations", "state_modified_compare_more_unrendered_values": false, "resource_types": [], "populate_cache": true, "introspect": true, "quiet": false, "use_colors_file": true, "log_level": "info", "write_json": true, "warn_error_options": {"error": [], "warn": [], "silence": []}, "favor_state": false, "source_freshness_run_project_hooks": true, "require_resource_names_without_spaces": true, "use_colors": true, "print": true, "macro_debugging": false, "use_fast_test_edges": false, "require_explicit_package_overrides_for_builtin_materializations": true, "require_nested_cumulative_type_params": false, "log_file_max_bytes": 10485760, "log_format": "default", "exclude": [], "indirect_selection": "eager"}}